# CLAUDE.md - Project Instructions

## Core Principles
- Do what has been asked; nothing more, nothing less
- NEVER create files unless absolutely necessary
- ALWAYS prefer editing existing files to creating new ones
- NEVER proactively create documentation files unless explicitly requested

## Project Features
- **Translation**: NOT IMPLEMENTED - No i18n/l10n support, single language only
- **Database**: MySQL 8 only, no PostgreSQL support
- **API Testing**: Bruno only, no Swagger/OpenAPI UI
- **Build**: Always use `./bin/` folder for all build outputs

---

# Backlog.md CLI Tool

## Task Management
- Tasks: `backlog/tasks/`, Drafts: `backlog/drafts/`
- Docs: `backlog/docs/`, Decisions: `backlog/decisions/`
- Tasks must be atomic, testable, and independent

## Task Structure
```markdown
# task-X - Title

## Description (why)
Brief explanation of purpose and goal.

## Acceptance Criteria (what)
- [ ] Outcome-oriented, testable criteria
- [ ] Focus on results, not implementation steps

## Implementation Plan (how)
1. Steps to achieve the task

## Implementation Notes (after completion)
- Approach taken, technical decisions, modified files
```

## Definition of Done
Task is complete when ALL criteria are met:
1. ✅ All acceptance criteria checked
2. ✅ Implementation notes added
3. ✅ Tests pass and linting succeeds
4. ✅ Documentation updated
5. ✅ Status set to "Done" via CLI
6. ✅ Completion notification sent

## Key Commands
```bash
# Basic workflow
backlog task list -s "To Do" --plain
backlog task 42 --plain
backlog task edit 42 -a @{yourself} -s "In Progress"
backlog task edit 42 -s Done --notes "Implementation summary"

# Creation
backlog task create "Title" -d "Description" --ac "Criterion 1,Criterion 2"
backlog task create -p 42 "Subtask"  # Parent task 42
```

**AI Agent Note**: Always use `--plain` flag for AI-friendly output.

## Task Completion Notification
After completing any task, always run:
```bash
terminal-notifier -message "[Task completed message]" -title "[Task title]" -sound "Submarine" -timeout 10
```
---

# Database System - MySQL 8 Only

**IMPORTANT**: MySQL 8.0.16+ only. No PostgreSQL support.

## Design Conventions
- **ID Fields**: Use `INT UNSIGNED` (not BIGINT) for all primary/foreign keys
- **Tables**: Module prefix required (`auth_`, `user_`, `tenant_`, etc.)
- **Migrations**: One table per migration file in `internal/database/migrations/`
- **Soft Delete**: Use status-based approach (not `deleted_at`)

## Module Organization
- `a_tenant/` (001-099) → `b_website/` (101-199) → `c_user/` (201-299) 
- `d_auth/` (301-399) → `e_rbac/` (401-499) → `f_onboarding/` (501-599)

## Migration Commands
```bash
make migrate-up                    # Run all migrations
make migrate-up MODULE=d_auth     # Run specific module
make migrate-status               # Check status
make migrate-down                 # Rollback last
```

## MySQL Features Used
- JSON data type with defaults: `JSON DEFAULT (JSON_OBJECT())`
- CHECK constraints for data validation
- ON UPDATE CURRENT_TIMESTAMP for auto-timestamps
- utf8mb4 character set for full Unicode support

---

# API Testing - Bruno Only

**IMPORTANT**: Use Bruno for API testing, not Swagger/OpenAPI UI.

## Bruno Setup
```bash
npm install -g @usebruno/cli
bru run api-tests/bruno --env local           # Run all tests
bru run api-tests/bruno/Auth --env local      # Run specific collection
```

## Collections Structure
- Base: `/api-tests/bruno/`
- Modules: `/Auth/`, `/User/`, `/Tenant/`
- Environments: `/environments/`

## Best Practices
- Clear naming: `Login.bru`, `Get User Profile.bru`
- Organize by module: Auth/, User/, Tenant/
- Include test assertions for status/data validation
- Commit Bruno files to Git for version control

---

# Soft Delete Strategy - Status-Based

**IMPORTANT**: Use status-based soft deletes, not `deleted_at` timestamps.

## Implementation
- **Status Field**: ENUM with 'deleted' option
- **Query Pattern**: `WHERE status != 'deleted'`
- **Update Pattern**: `UPDATE table SET status = 'deleted'`

## Common Status Values
- Users: `'active', 'inactive', 'suspended', 'pending_verification', 'deleted'`
- Posts: `'draft', 'published', 'archived', 'scheduled', 'deleted'`
- Comments: `'pending', 'approved', 'rejected', 'spam', 'deleted'`

---

# Module Directory Structure

**IMPORTANT**: All modules under `internal/modules/` only.

## Structure
- `internal/modules/{module_name}/`
  - `models/` - Domain models and entities
  - `repositories/` - Data access layer
  - `services/` - Business logic layer
  - `handlers/` - HTTP handlers
  - `routes.go` - Route registration

## Import Paths
- **Correct**: `github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models`
- **Incorrect**: `github.com/tranthanhloi/wn-api-v3/modules/tenant/models`

---

# Multi-Tenant Database Schema

**Status**: ✅ COMPLETED - Auth, RBAC, User, and Blog modules are tenant-scoped

## Completed Modules
- **Auth Module**: All auth tables (tokens, sessions, attempts, etc.) tenant-scoped
- **RBAC Module**: User role assignments tenant-isolated
- **User Module**: User profiles, preferences, social links tenant-scoped  
- **Blog Module**: Posts, categories, tags, schedules, revisions tenant-scoped

## Multi-Tenant Migration Rules
**IMPORTANT**: Always edit original migration files, never create separate "add_tenant_id" files.

### Standard Pattern
```sql
CREATE TABLE IF NOT EXISTS table_name (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    -- other columns...
    
    CONSTRAINT fk_table_name_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_name_tenant_field (tenant_id, field_name),
    INDEX idx_table_name_tenant_id (tenant_id)
);
```

## Required Actions for New Multi-Tenant Tables
1. Edit original migration file to add `tenant_id INT UNSIGNED NOT NULL`
2. Add foreign key constraint to tenants table
3. Update unique constraints to include tenant_id
4. Add tenant_id indexes for performance
5. Update Go models to include `TenantID uint` field

---

# MCP Qdrant Vector Database

**Status**: ✅ Infrastructure configured, collection `blog-api-v3` ready

## Configuration
- **Qdrant**: ports 6333 (HTTP), 6334 (gRPC)
- **MCP**: port 9058 for AI assistant integration
- **Storage**: `~/qdrant_storage` volume mounted

## MCP Tools
- **Store**: `mcp__qdrant__qdrant-store` 
- **Search**: `mcp__qdrant__qdrant-find`

## N8N Integration
- API URL: `http://docker.host.internal:9077/api/cms/v1`

---

# Email Verification System

**Status**: ✅ FULLY IMPLEMENTED (2025-01-18)

## Overview
Email verification system with rate limiting, token management, and notification integration.

## Key Components
- **Migration**: `308_create_auth_email_verification_tokens_table.up.sql`
- **Models**: Email verification token entities and DTOs
- **Repository**: Token CRUD operations, validation, cleanup
- **Service**: Token creation, rate limiting (3 resends max, 5-min intervals), email sending
- **Handler**: Verification endpoints

## API Endpoints
- `POST /auth/verify-email` - Verify email with token
- `POST /auth/resend-verification` - Resend verification email (rate limited)
- `GET /auth/verification-status` - Check verification status (public)
- `GET /auth/verification-token-stats` - Get token statistics (protected)

## User Flow
1. **Registration** → User created with `pending_verification` status
2. **Email Sent** → Verification email via notification service
3. **Verification** → Token verified, user status → `active`
4. **Login** → Verified users can login, unverified blocked with 403

## Configuration
- **Token Expiry**: 24 hours default
- **Rate Limiting**: 3 resends max, 5-minute intervals
- **Templates**: ID 7 (initial), ID 8 (resend)
- **Settings**: Enable/disable via `RequireEmailVerification` in AuthConfig

## Security Features
- 32-byte random tokens with SHA-256 hashing
- One active token per user email
- Tenant-scoped tokens
- Rate limiting protection

---

# Registration vs Onboarding Flow - CRITICAL ARCHITECTURE

**IMPORTANT**: Registration (auth) separated from onboarding (tenant creation).

## Flow Architecture

### 1. Registration Flow (Auth Module)
- **Purpose**: Create user account for authentication only
- **Scope**: Creates user in `users` table (NO tenant_id field)
- **Endpoint**: `POST /auth/register`
- **Response**: User data + authentication tokens
- **Next**: Redirect to onboarding

### 2. Onboarding Flow (Onboarding Module)
- **Purpose**: Setup organization/tenant for authenticated user
- **Requires**: Valid JWT token from registration
- **Creates**: New tenant + tenant membership with `is_primary: true`
- **Endpoint**: `POST /onboarding/create-organization`

## Database Schema
```sql
-- Users table: NO tenant_id field (global entities)
CREATE TABLE users (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    -- NO tenant_id field here
);

-- Tenant memberships: Manages all user-tenant relationships
CREATE TABLE tenant_memberships (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    status VARCHAR(50) DEFAULT 'active'
);
```

## Implementation Rules

### Registration Endpoint
- ✅ **DO**: Create user account, generate auth tokens, send email verification
- ❌ **DON'T**: Create tenant, create tenant membership, assign user to tenant

### Onboarding Endpoint  
- ✅ **DO**: Require JWT, create tenant, create tenant membership with `is_primary: true`
- ❌ **DON'T**: Create user, handle authentication

## Authentication & Authorization
- **JWT Tokens**: User-based (not tenant-based)
- **Headers**: `Authorization: Bearer <token>` + `X-Tenant-ID: <tenant_id>`
- **Validation**: Verify user access to requested tenant via tenant_memberships

---

# Build và Test Guidelines

**IMPORTANT**: Always use `./bin/` folder for all build outputs.

## Build Commands
```bash
# Correct - build to ./bin/
go build -o ./bin/server ./cmd/server/main.go
go test -c -o ./bin/test_binary ./internal/modules/auth/services

# Wrong - creates files in root
go build -o server ./cmd/server/main.go    # ❌
```

## Organization
```
./bin/
├── server           # Main server binary
├── test_*          # Test binaries
├── bench_*         # Benchmark binaries
└── temp_*          # Temporary files
```

## .gitignore
```gitignore
/bin/
!/bin/.gitkeep
```

## Development Workflow
1. **Build**: Always use `./bin/` prefix
2. **Clean**: Regular `rm -rf ./bin/*` cleanup
3. **Commit**: Never commit files in `./bin/`

---

# API Design Guidelines

**IMPORTANT**: All APIs must define clear request and response structures.

## API Design Rules
- **Request Definition**: Every API endpoint must have clearly defined request structures (DTOs/models)
- **Response Definition**: Every API endpoint must have clearly defined response structures 
- **Documentation**: All request/response structures must be documented with Swagger annotations
- **Consistency**: Use standardized response format with Status, Data, and Meta fields
- **Validation**: All request structures must include proper validation tags
# Migration, Seeds
- internal/database/migrations: luôn chỉnh sửa file migration đã có thay vì ALTER TABLE
- internal/database/seeders: luôn viết seed vào đây thay vì viết migration