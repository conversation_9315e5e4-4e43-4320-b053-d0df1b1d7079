package notification

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// RegisterRoutes registers notification module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, logger utils.Logger) {
	// Initialize repositories
	notificationRepo := repositories.NewNotificationRepository(db)
	recipientRepo := repositories.NewRecipientRepository(db)
	templateRepo := repositories.NewTemplateRepository(db)
	logRepo := repositories.NewLogRepository(db)

	// Initialize services
	templateService := services.NewTemplateService(templateRepo)
	emailProvider := services.NewSMTPProvider(
		utils.GetEnv("SMTP_HOST", "localhost"),
		utils.GetEnvAsInt("SMTP_PORT", 1025),
		"", // No username needed for MailCatcher
		"", // No password needed for MailCatcher
		"Blog API",
		utils.GetEnv("SMTP_FROM", "<EMAIL>"),
		logger,
	)
	socketProvider := services.NewMockSocketProvider(logger)
	deliveryService := services.NewDeliveryService(notificationRepo, recipientRepo, templateRepo, logRepo, templateService, emailProvider, socketProvider, logger)
	notificationService := services.NewNotificationService(notificationRepo, recipientRepo, templateRepo, logRepo, deliveryService)

	// Initialize handlers
	notificationHandler := handlers.NewSimpleNotificationHandler(notificationService)
	templateHandler := handlers.NewTemplateHandler(templateService)
	trackingHandler := handlers.NewTrackingHandler(recipientRepo, logRepo)

	// Notification routes
	notifications := router.Group("/notifications")
	{
		notifications.POST("", notificationHandler.CreateNotification)
		notifications.GET("", notificationHandler.ListNotifications)
		notifications.GET("/me", notificationHandler.GetUserNotifications)
		notifications.GET("/:id", notificationHandler.GetNotification)
		notifications.POST("/:id/send", notificationHandler.SendNotification)
	}

	// Template routes
	templates := router.Group("/templates")
	{
		templates.POST("", templateHandler.CreateTemplate)
		templates.GET("", templateHandler.ListTemplates)
		templates.GET("/:id", templateHandler.GetTemplate)
		templates.PUT("/:id", templateHandler.UpdateTemplate)
		templates.DELETE("/:id", templateHandler.DeleteTemplate)
		templates.POST("/:id/activate", templateHandler.ActivateTemplate)
		templates.POST("/:id/deactivate", templateHandler.DeactivateTemplate)
		templates.POST("/:id/versions", templateHandler.CreateTemplateVersion)
		templates.GET("/:id/versions", templateHandler.ListTemplateVersions)
		templates.PUT("/:id/versions/:version_id", templateHandler.UpdateTemplateVersion)
		templates.POST("/:id/versions/:version_id/activate", templateHandler.ActivateTemplateVersion)
		templates.POST("/:id/versions/:version_id/preview", templateHandler.PreviewTemplate)
	}

	// Tracking routes
	tracking := router.Group("/tracking")
	{
		tracking.POST("/:tenant_id/recipients/:recipient_id/open", trackingHandler.TrackOpen)
		tracking.POST("/:tenant_id/recipients/:recipient_id/click", trackingHandler.TrackClick)
		tracking.GET("/:tenant_id/recipients/:recipient_id/pixel", trackingHandler.GetPixel)
		tracking.GET("/:tenant_id/recipients/:recipient_id/redirect", trackingHandler.RedirectAndTrack)
	}
}

// RegisterWebhookRoutes registers webhook routes for notification providers
func RegisterWebhookRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize repositories for webhook handling
	recipientRepo := repositories.NewRecipientRepository(db)
	logRepo := repositories.NewLogRepository(db)

	webhooks := router.Group("/webhooks/notifications")
	{
		// Email provider webhooks
		webhooks.POST("/sendgrid", handleSendGridWebhook(recipientRepo, logRepo))
		webhooks.POST("/mailgun", handleMailgunWebhook(recipientRepo, logRepo))
		webhooks.POST("/ses", handleSESWebhook(recipientRepo, logRepo))

		// Push notification webhooks
		webhooks.POST("/fcm", handleFCMWebhook(recipientRepo, logRepo))
		webhooks.POST("/apns", handleAPNSWebhook(recipientRepo, logRepo))

		// SMS provider webhooks
		webhooks.POST("/twilio", handleTwilioWebhook(recipientRepo, logRepo))
		webhooks.POST("/sns", handleSNSWebhook(recipientRepo, logRepo))
	}
}

// Webhook handlers (placeholder implementations)
func handleSendGridWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement SendGrid webhook handling
		// - Parse webhook payload
		// - Update recipient status
		// - Create log entries
		// - Handle bounces, complaints, etc.
		c.JSON(200, gin.H{"status": "ok"})
	}
}

func handleMailgunWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Mailgun webhook handling
		c.JSON(200, gin.H{"status": "ok"})
	}
}

func handleSESWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Amazon SES webhook handling
		c.JSON(200, gin.H{"status": "ok"})
	}
}

func handleFCMWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement FCM webhook handling
		c.JSON(200, gin.H{"status": "ok"})
	}
}

func handleAPNSWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement APNS webhook handling
		c.JSON(200, gin.H{"status": "ok"})
	}
}

func handleTwilioWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Twilio webhook handling
		c.JSON(200, gin.H{"status": "ok"})
	}
}

func handleSNSWebhook(recipientRepo repositories.RecipientRepository, logRepo repositories.LogRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Amazon SNS webhook handling
		c.JSON(200, gin.H{"status": "ok"})
	}
}
